import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { LazyLoad } from "Components/LazyLoad";
import { InteractiveButton } from "Components/InteractiveButton";
import { MkdInput } from "Components/MkdInput";
import { useContexts } from "Hooks/useContexts";
import MkdSDK from "Utils/MkdSDK";
import TreeSDK from "Utils/TreeSDK";

const FundManagerSelectionModal = ({
  isOpen,
  onClose,
  fundManagers = [],
  onSubmit,
  loading = false,
  onRemoveFundManager, // New prop for removing fund managers
}) => {
  const [createNewGroup, setCreateNewGroup] = useState(false);
  const { authState } = useContexts();
  const [groups, setGroups] = useState([]);
  const [fetching, setFetching] = useState(false);

  const schema = yup.object({
    group_id: yup
      .string()
      .required("Please select a group or create a new one"),
    new_group_name: createNewGroup
      ? yup.string().required("Group name is required")
      : yup.string(),
  });

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
    setValue,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: { group_id: "", new_group_name: "" },
  });

  const groupId = watch("group_id");

  // Fetch groups using the same logic as SelectGroupType
  const fetchGroups = async () => {
    setFetching(true);
    try {
      const sdk = new MkdSDK();
      const tdk = new TreeSDK();

      const [allGroupsRes, recipientGroupsRes] = await Promise.all([
        // All groups API - for groups that might not have recipient groups yet
        sdk.callRawAPI(
          `/v4/api/records/group?filter=user_id,in,'NULL',${authState.user}`
        ),
        // Use TreeSDK directly like the list page does
        tdk.getPaginate("recipient_group", {
          join: ["user", "group"],
          filter: [`goodbadugly_recipient_group.user_id,eq,${authState.user}`],
          size: 1000,
          page: 1,
          order: "id",
          direction: "desc",
        }),
      ]);

      const allGroups = allGroupsRes.list || [];
      const recipientGroups = recipientGroupsRes.list || [];

      // Create a map of group_id to recipient group data
      const recipientGroupMap = {};
      recipientGroups.forEach((rg) => {
        if (rg.group && rg.group.id) {
          const memberIds = rg.members
            ? rg.members.split(",").map((id) => id.trim())
            : [];
          recipientGroupMap[rg.group.id] = {
            recipient_group_id: rg.id,
            members: memberIds,
            group_name: rg.group.group_name,
          };
        }
      });

      // Start with all groups and enhance with recipient group data
      const mergedGroups = allGroups.map((group) => {
        const recipientData = recipientGroupMap[group.id];
        return {
          id: group.id,
          group_name: group.group_name,
          members: recipientData ? recipientData.members : [],
          recipient_group_id: recipientData
            ? recipientData.recipient_group_id
            : null,
          hasMembers: recipientData ? recipientData.members.length > 0 : false,
        };
      });

      // Add recipient groups that don't exist in allGroups (shouldn't happen but just in case)
      const allGroupIds = new Set(allGroups.map((g) => g.id));
      recipientGroups.forEach((rg) => {
        if (rg.group && !allGroupIds.has(rg.group.id)) {
          const memberIds = rg.members
            ? rg.members.split(",").map((id) => id.trim())
            : [];
          mergedGroups.push({
            id: rg.group.id,
            group_name: rg.group.group_name,
            members: memberIds,
            recipient_group_id: rg.id,
            hasMembers: memberIds.length > 0,
          });
        }
      });

      setGroups(mergedGroups);
    } catch (error) {
      console.error("Error fetching groups:", error);
    } finally {
      setFetching(false);
    }
  };

  useEffect(() => {
    if (authState?.user) {
      fetchGroups();
    }
  }, [authState?.user]);

  useEffect(() => {
    if (createNewGroup) {
      setValue("group_id", "new");
    } else {
      setValue("group_id", "");
      setValue("new_group_name", "");
    }
  }, [createNewGroup, setValue]);

  const handleFormSubmit = (data) => {
    onSubmit({
      ...data,
      fundManagers,
      isNewGroup: createNewGroup,
    });
  };

  const handleClose = () => {
    reset();
    setCreateNewGroup(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="mx-4 max-h-[90vh] w-full max-w-2xl overflow-y-auto rounded-lg bg-brown-main-bg shadow-xl">
        {/* Header */}
        <div className="flex items-center justify-between border-b p-6">
          <h2 className="font-iowan text-xl font-semibold">
            Add Investors to Group
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 transition-colors hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Selected Fund Managers Summary */}
          <div className="mb-6 rounded-lg bg-brown-main-bg/70 p-4">
            <div className="mb-3 flex items-center gap-4">
              <span className="text-3xl font-bold text-primary-black">
                {fundManagers.length}
              </span>
              <span className="text-lg font-medium">
                Investor{fundManagers.length !== 1 ? "s" : ""} Selected
              </span>
            </div>

            {/* Fund Manager Names as Chips */}
            <div className="space-y-2">
              <p className="mb-2 text-sm font-medium text-gray-700">
                Selected:
              </p>
              <div className="flex max-h-32 flex-wrap gap-2 overflow-y-auto">
                {fundManagers.map((manager) => (
                  <div
                    key={manager.id}
                    className="flex items-center gap-2 rounded-full border border-gray-300 bg-gray-100 px-3 py-1 text-sm"
                    title={`${manager.first_name} ${manager.last_name} from ${manager.fund_name}`}
                  >
                    <span className="text-gray-700">
                      {manager.first_name} {manager.last_name}
                    </span>
                    {onRemoveFundManager && (
                      <button
                        onClick={() => onRemoveFundManager(manager.id)}
                        className="ml-1 text-gray-500 transition-colors hover:text-red-600"
                        title="Remove from selection"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
            {/* Group Selection Toggle */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-gray-700">
                Choose an option:
              </label>

              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={!createNewGroup}
                    onChange={() => setCreateNewGroup(false)}
                    className="mr-2"
                  />
                  <span>Add to existing group</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={createNewGroup}
                    onChange={() => setCreateNewGroup(true)}
                    className="mr-2"
                  />
                  <span>Create new group</span>
                </label>
              </div>
            </div>

            {/* Existing Group Selection */}
            {!createNewGroup && (
              <div>
                <LazyLoad>
                  <MkdInput
                    type="select"
                    register={register}
                    name="group_id"
                    label="Select Existing Group"
                    errors={errors}
                    options={groups?.map((group) => group.group_name) || []}
                    noneText="Choose a group..."
                    className="!h-[2.75rem] !w-full !rounded-md !border"
                  />
                </LazyLoad>
              </div>
            )}

            {/* New Group Name */}
            {createNewGroup && (
              <div>
                <LazyLoad>
                  <MkdInput
                    type="text"
                    register={register}
                    name="new_group_name"
                    label="New Group Name"
                    errors={errors}
                    placeholder="Enter group name..."
                    className="!h-[2.75rem] !w-full !rounded-md !border"
                  />
                </LazyLoad>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <LazyLoad>
                <InteractiveButton
                  type="submit"
                  loading={isSubmitting || loading}
                  disabled={isSubmitting || loading}
                  className="flex-1 rounded-md bg-primary-black px-4 py-2 font-medium text-white transition-colors hover:bg-gray-800"
                >
                  Add to Group
                </InteractiveButton>
              </LazyLoad>

              <button
                type="button"
                onClick={handleClose}
                disabled={isSubmitting || loading}
                className="flex-1 rounded-md border border-black bg-transparent px-4 py-2 font-medium text-gray-800 transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default FundManagerSelectionModal;
