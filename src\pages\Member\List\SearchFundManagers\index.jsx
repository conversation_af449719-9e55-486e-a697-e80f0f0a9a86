import React, { useEffect, useRef, useState } from "react";
import SearchAndFilterFundManager from "./SearchAndFilterFundManager";
import { LazyLoad } from "Components/LazyLoad";
import { MkdListTableV2 } from "Components/MkdListTable";
import { ArrowLeftIcon, SearchIcon } from "Assets/svgs";
import { useSubscription } from "Hooks/useSubscription";
import { useProfile } from "Hooks/useProfile";
import { PlanFundManagerAccess } from "Utils/config";
import { useNavigate } from "react-router";
import { useContexts } from "Hooks/useContexts";

// Function to format stages text
const formatStages = (stages) => {
  if (!stages) return "";

  // Split by comma and clean up each stage
  return stages
    .split(",")
    .map((stage) => stage.trim())
    .map((stage) => {
      // Add space after "Series" and before letters/numbers
      return stage
        .replace(/Series([A-Z])/g, "Series $1")
        .replace(/Series([A-Z]\+)/g, "Series $1")
        .replace(/,/g, ", ");
    })
    .join(", ");
};

// Process function to format the stages column data
const processStagesData = (data) => {
  return data.map((item) => ({
    ...item,
    stages: formatStages(item.stages),
  }));
};

const fundManagerColumns = [
  {
    header: "Company",
    accessor: "fund_name",
    isSorted: true,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    type: "fund_manager",
  },
  {
    header: "Stages",
    accessor: "stages",
    isSorted: true,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    type: "fund_manager",
  },

  {
    header: "First Name",
    accessor: "first_name",
    isSorted: true,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    type: "fund_manager",
  },

  {
    header: "Last Name",
    accessor: "last_name",
    isSorted: true,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    type: "fund_manager",
  },

  {
    header: "Title",
    accessor: "title",
    isSorted: true,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: false, // Hide the Title column
    type: "fund_manager",
  },
  {
    header: "Country",
    accessor: "based_in_country",
    isSorted: true,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    type: "fund_manager",
  },
  {
    header: "City",
    accessor: "based_in_city",
    isSorted: true,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    type: "fund_manager",
  },
  {
    header: "State",
    accessor: "based_in_state",
    isSorted: true,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    type: "fund_manager",
  },
];

const SearchFundManagersPage = () => {
  const updateFundManagerRef = useRef(null);
  const refreshFundManagerRef = useRef(null);

  const { setGlobalState } = useContexts();

  const [filter, setFilter] = useState("");
  const [selectedFundManagers, setSelectedFundManagers] = useState([]);
  const [fundManagers, setFundManagers] = useState([]);
  const [localData, setLocalData] = useState({
    maxSelection: 0,
  });

  // Default filter for United States
  const defaultFilter = [
    "goodbadugly_fund_manager.based_in_country,cs,United States",
  ];

  const navigate = useNavigate();
  const { profile } = useProfile({});
  const { data, getSubscription, getAccessedFundManager, loading } =
    useSubscription();

  console.log("data", data);

  const availableFundManagers =
    PlanFundManagerAccess?.[
      data?.object?.plan?.nickname?.split(" ")?.[0]?.trim()
    ];

  const addFundManager = () => {
    const fundManagersToAdd = fundManagers
      ?.map((fundManager) => {
        if (selectedFundManagers?.includes(fundManager?.id)) {
          return fundManager;
        }
      })
      .filter(Boolean);

    setGlobalState("fundManagersToAdd", fundManagersToAdd);
    navigate("/member/add-recipient_group");
  };

  const determineMaxSelection = () => {
    if (!data?.accessedFundManagers) {
      return setLocalData((prev) => {
        return {
          ...prev,
          maxSelection: availableFundManagers,
        };
      });
    }

    const remainder = availableFundManagers - data?.accessedFundManagers;
    return setLocalData((prev) => {
      return {
        ...prev,
        maxSelection: remainder,
      };
    });
  };

  useEffect(() => {
    if (profile?.id) {
      getSubscription({
        filter: [
          `user_id,eq,${profile?.id}`,
          `cancelled,eq,0`,
          `status,eq,'active'`,
        ],
        join: [],
      });

      getAccessedFundManager(profile);
    }
  }, [profile?.id]);

  useEffect(() => {
    if (refreshFundManagerRef?.current) {
      refreshFundManagerRef?.current?.click();
    }
  }, [filter]);

  useEffect(() => {
    determineMaxSelection();
  }, [data?.accessedFundManagers, availableFundManagers]);

  return (
    <>
      <LazyLoad>
        <div className="flex items-center justify-between gap-5 p-7">
          <button
            className=" flex h-[2.25rem] w-[5.1875rem] items-center justify-center gap-2 "
            onClick={() => navigate(-1)}
          >
            <div className="flex min-h-[32px] min-w-[32px] items-center justify-center  gap-2 rounded border border-[#1f1d1a] ">
              <svg
                className="min-h-5 min-w-5"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M17.5 10.0001L2.5 10.0001"
                  stroke="black"
                  stroke-width="1.66667"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M8.33203 15.8335L2.4987 10.0002L8.33203 4.16683"
                  stroke="black"
                  stroke-width="1.66667"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
            <span className="font-inter font-[400]"> Back</span>
          </button>

          {data?.subscription?.status === "active" ? (
            <div className="flex items-center justify-center gap-2">
              {data?.accessedFundManagers ? (
                <span>
                  {availableFundManagers - data?.accessedFundManagers} /{" "}
                  {availableFundManagers}
                </span>
              ) : (
                <span>{availableFundManagers}</span>
              )}
              <span>Available investors </span>
            </div>
          ) : null}
        </div>

        <div className={`flex items-center justify-between gap-5 px-7`}>
          <p className={`font-iowan text-[2rem] font-bold leading-[2.4863rem]`}>
            Investors
          </p>

          <div>
            <div className="flex h-[2.25rem] cursor-pointer items-center justify-between gap-3 rounded-[.125rem] border border-black px-2 py-1">
              <SearchIcon className="!h-[1.25rem] !w-[1.25rem] text-black" />
              <input
                type="text"
                placeholder={`Search Fund Managers`}
                className="w-full border-none bg-transparent p-0 placeholder:text-left placeholder:text-black focus:outline-none"
                style={{ boxShadow: "0 0 transparent" }}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                  }
                }}
                onKeyUp={(e) => {}}
              />
              {/* <AiOutlineClose className="text-lg text-gray-200" /> */}
            </div>
          </div>
        </div>
        <div className="custom-overflow w-full space-y-6 overflow-x-auto p-7">
          <SearchAndFilterFundManager
            setFilter={setFilter}
            selectedFundManagers={selectedFundManagers}
            onAddToGroup={() => {
              addFundManager();
            }}
          />

          <LazyLoad>
            <MkdListTableV2
              showSearch={false}
              useDefaultColumns={true}
              defaultColumns={fundManagerColumns}
              processes={[processStagesData]}
              noDataComponent={{
                use: true,
                component: <LazyLoad>{/* <NoRecentUpdates /> */}</LazyLoad>,
              }}
              // onUpdateCurrentTableData={(cb) => {
              //   cb(externalData);
              // }}
              // externalData={{
              //   use: true,
              //   fetch: fetchData,
              // }}
              onReady={(data) => {
                setFundManagers(data);
              }}
              hasFilter={false}
              tableRole={"member"}
              table={"fund_manager"}
              actionId={"id"}
              tableTitle={<></>}
              defaultFilter={[
                ...defaultFilter,
                ...(filter?.split("|").filter(Boolean) || []),
              ]}
              actions={{
                view: { show: false, action: null, multiple: false },
                select: {
                  show: true,
                  action: (ids) => {
                    setSelectedFundManagers(() => [...ids]);
                  },
                  multiple: true,
                  max: localData?.maxSelection,
                },
                export: { show: false, action: null, multiple: true },
                delete: { show: false, action: null, multiple: true },
              }}
              defaultPageSize={20}
              showPagination={true}
              updateRef={updateFundManagerRef}
              refreshRef={refreshFundManagerRef}
              showScrollbar={false}
              maxHeight={`md:grid-rows-[inherit] grid-rows-[inherit]`}
            />
          </LazyLoad>
        </div>
      </LazyLoad>
    </>
  );
};

export default SearchFundManagersPage;
{
  /* <div className="flex flex-row justify-end">
{selectedRows.length === 0 ? null : (
  <SelectedFundManagersButton selectedRows={selectedRows} />
)}
</div> */
}
{
  /* <ResponsiveTableWrapper>
            <table className="w-full divide-y divide-[#1f1d1a]/10">
              <thead>
                <tr>
                  <th
                    scope="col"
                    className={`font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3`}
                    onClick={() => handleSort("fund_name")}
                    style={{ cursor: "pointer" }}
                  >
                    <div className="flex flex-row">
                      Company {getSortLabel("fund_name")}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className={`font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3`}
                    onClick={() => handleSort("stages")}
                    style={{ cursor: "pointer" }}
                  >
                    <div className="flex flex-row">
                      Stage {getSortLabel("stages")}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className={`font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3`}
                    onClick={() => handleSort("first_name")}
                    style={{ cursor: "pointer" }}
                  >
                    <div className="flex flex-row">
                      First Name {getSortLabel("first_name")}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className={`font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3`}
                    onClick={() => handleSort("last_name")}
                    style={{ cursor: "pointer" }}
                  >
                    <div className="flex flex-row">
                      Last Name {getSortLabel("last_name")}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className={`font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3`}
                    onClick={() => handleSort("title")}
                    style={{ cursor: "pointer" }}
                  >
                    <div className="flex flex-row">
                      Title {getSortLabel("title")}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className={`font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3`}
                    onClick={() => handleSort("geographies_investing_in")}
                    style={{ cursor: "pointer" }}
                  >
                    <div className="flex flex-row">
                      Country {getSortLabel("geographies_investing_in")}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className={`font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3`}
                    onClick={() => handleSort("based_in_city")}
                    style={{ cursor: "pointer" }}
                  >
                    <div className="flex flex-row">
                      City {getSortLabel("based_in_city")}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className={`font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3`}
                    onClick={() => handleSort("based_in_state")}
                    style={{ cursor: "pointer" }}
                  >
                    <div className="flex flex-row">
                      State {getSortLabel("based_in_state")}
                    </div>
                  </th>
                  {/* {columns.map((item) => (
                  <th
                    key={item.id}
                    scope="col"
                   className={`whitespace-nowrap  sm:border-b-[3px] md:border-dashed sm:border-0  border-b-[#1f1d1a]/30 px-6 py-3 text-left  capitalize tracking-wider text-[#1f1d1a]`}
                  >
                    {item.title}
                  </th>
                ))} 
                </tr>
              </thead>

              <tbody className="font-iowan-regular divide-y divide-[#1f1d1a]/10">
                {data?.map((item) => (
                  <tr key={item.id}>
                    <td className="px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6">
                      {item.fund_name}
                    </td>
                    <td className="px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6">
                      {item.stages}
                    </td>
                    <td className="px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6">
                      {item.first_name}
                    </td>
                    <td className="px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6">
                      {item.last_name}
                    </td>
                    <td className="px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6">
                      {item.title}
                    </td>
                    <td className="px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6">
                      {item.based_in_country}
                    </td>
                    <td className="px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6">
                      {item.based_in_city}
                    </td>
                    <td className="px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6">
                      {item.based_in_state}
                    </td>
                    
                  </tr>
                ))}
              </tbody>
            </table>
          </ResponsiveTableWrapper> */
}
